import { TestBed } from '@angular/core/testing';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { Subject, of, throwError } from 'rxjs';
import { COMMON_SELECT_FILTER, DEVICE_ALREADY_CLIENT, DEVICE_ALREADY_DEMO, DEVICE_ALREADY_EDIT_DISABLE, DEVICE_ALREADY_EDIT_ENABLE, DEVICE_ALREADY_LOCKED, DEVICE_ALREADY_TEST, DEVICE_ALREADY_UNLOCKED, DEVICE_CONVERT_TO_CLIENT, DEVICE_CONVERT_TO_DEMO, DEVICE_CONVERT_TO_TEST, DEVICE_HAS_NO_SERIAL_NUMBER, DEVICE_STATUS_NOT_ENABLE, DeviceDetailResource, DeviceListResource, SALES_ORDER_PARTIALLY_CONFIGURED } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { AssignSelectedReleaseVersionRequest } from 'src/app/model/device/AssignSelectedReleaseVersionRequest.model';
import { DeviceExportCSVSearchRequest } from 'src/app/model/device/DeviceExportCSVSearchRequest.model';
import { DeviceFilterAction } from 'src/app/model/device/DeviceFilterAction.model';
import { DeviceSearchRequest } from 'src/app/model/device/deviceSearchRequest.model';
import { TransferProductDetails } from 'src/app/model/device/TransferProductDetails.model';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { DeviceService } from 'src/app/shared/device.service';
import { deviceTypesEnum } from 'src/app/shared/enum/deviceTypesEnum.enum';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { CountryCacheService } from 'src/app/shared/Service/CacheService/countrycache.service';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { DownloadService } from 'src/app/shared/util/download.service';
import { ModuleValidationServiceService } from 'src/app/shared/util/module-validation-service.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { DeviceOperationService } from './device-operation.service';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { ValidationService } from 'src/app/shared/util/validation.service';
import { EnumMapping } from 'src/app/model/common/EnumMapping.model';


describe('DeviceOperationService', () => {
  let service: DeviceOperationService;
  let deviceService: jasmine.SpyObj<DeviceService>;
  let salesOrderApiCallService: jasmine.SpyObj<SalesOrderApiCallService>;
  let countryCacheService: jasmine.SpyObj<CountryCacheService>;
  let commonsService: jasmine.SpyObj<CommonsService>;
  let exceptionHandlingService: jasmine.SpyObj<ExceptionHandlingService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let downloadService: jasmine.SpyObj<DownloadService>;
  let moduleValidationService: jasmine.SpyObj<ModuleValidationServiceService>;
  let permissionService: jasmine.SpyObj<PermissionService>;
  let confirmDialogService: jasmine.SpyObj<ConfirmDialogService>;


  beforeEach(() => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const deviceServiceSpy = jasmine.createSpyObj('DeviceService', [
      'getpackageVersion', 'getDeviceList', 'updateDeviceTypeToTest', 'updateDeviceTypeToClient',
      'updateDeviceTypeToDemo', 'updateDeviceState', 'editEnableDisableForDevice',
      'disableProductStatusForDevice', 'rmaProductStatusForDevice', 'associationDeviceWithSalesOrder',
      'generateCSVFileForDevice', 'downloadCSVFileForDevice', 'getDeviceDetail',
      'getReleaseVersionDetail', 'assignSelectedReleaseVersion'
    ]);
    const salesOrderApiCallServiceSpy = jasmine.createSpyObj('SalesOrderApiCallService', ['getSalesOrderNumberList']);
    const countryCacheServiceSpy = jasmine.createSpyObj('CountryCacheService', ['getCountryListFromCache']);
    const commonsServiceSpy = jasmine.createSpyObj('CommonsService', [
      'checkForNull', 'checkValueIsNullOrEmpty', 'checkNullFieldValue', 'getIdsFromArray',
      'getSelectedValueFromEnum', 'getSelectedValueFromBooleanKeyValueMapping', 'getDeviceTypeStringToEnum'
    ]);
    const exceptionHandlingServiceSpy = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    const downloadServiceSpy = jasmine.createSpyObj('DownloadService', ['downloadExportCSV']);
    const moduleValidationServiceSpy = jasmine.createSpyObj('ModuleValidationServiceService', [
      'validateWithEditableWithMultipalRecoard', 'validateWithUserCountryForMultileRecord',
      'validateWithEditStateForSingleRecord', 'validateWithUserCountryForSingleRecord'
    ]);
    const permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getDevicePermission']);
    const confirmDialogServiceSpy = jasmine.createSpyObj('ConfirmDialogService', [
      'getBasicModelConfigForDisableAction', 'confirm', 'getErrorMessageDisableToRma'
    ]);
    const enumMappingDisplayNamePipeSpy = jasmine.createSpyObj('EnumMappingDisplayNamePipe', ['transform']);
    const validationServiceSpy = jasmine.createSpyObj('ValidationService', ['validateProductStatusForRMAAction']);

    TestBed.configureTestingModule({
      providers: [
        { provide: DeviceService, useValue: deviceServiceSpy },
        { provide: SalesOrderApiCallService, useValue: salesOrderApiCallServiceSpy },
        { provide: CountryCacheService, useValue: countryCacheServiceSpy },
        { provide: CommonsService, useValue: commonsServiceSpy },
        { provide: ExceptionHandlingService, useValue: exceptionHandlingServiceSpy },
        { provide: DownloadService, useValue: downloadServiceSpy },
        { provide: ModuleValidationServiceService, useValue: moduleValidationServiceSpy },
        { provide: PermissionService, useValue: permissionServiceSpy },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceSpy },
        { provide: EnumMappingDisplayNamePipe, useValue: enumMappingDisplayNamePipeSpy },
        { provide: ValidationService, useValue: validationServiceSpy },
        AuthJwtService,
        LocalStorageService,
        SessionStorageService,
        PrintListPipe,
        commonsProviders(toastrServiceMock)
      ]
    });

    service = TestBed.inject(DeviceOperationService);
    deviceService = TestBed.inject(DeviceService) as jasmine.SpyObj<DeviceService>;
    salesOrderApiCallService = TestBed.inject(SalesOrderApiCallService) as jasmine.SpyObj<SalesOrderApiCallService>;
    countryCacheService = TestBed.inject(CountryCacheService) as jasmine.SpyObj<CountryCacheService>;
    commonsService = TestBed.inject(CommonsService) as jasmine.SpyObj<CommonsService>;
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService) as jasmine.SpyObj<ExceptionHandlingService>;
    downloadService = TestBed.inject(DownloadService) as jasmine.SpyObj<DownloadService>;
    moduleValidationService = TestBed.inject(ModuleValidationServiceService) as jasmine.SpyObj<ModuleValidationServiceService>;
    permissionService = TestBed.inject(PermissionService) as jasmine.SpyObj<PermissionService>;
    confirmDialogService = TestBed.inject(ConfirmDialogService) as jasmine.SpyObj<ConfirmDialogService>;

  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  // ==================== SUBJECT TESTS ====================

  it('should get device list loading subject', () => {
    expect(service.getDeviceListLoadingSubject()).toBeInstanceOf(Subject);
  });

  it('should call device list loading subject', () => {
    spyOn(service.getDeviceListLoadingSubject(), 'next');
    service.callDeviceListLoadingSubject(true);
    expect(service.getDeviceListLoadingSubject().next).toHaveBeenCalledWith(true);
  });

  it('should get device detail loading subject', () => {
    expect(service.getDeviceDetailLoadingSubject()).toBeInstanceOf(Subject);
  });

  it('should call device detail loading subject', () => {
    spyOn(service.getDeviceDetailLoadingSubject(), 'next');
    service.callDeviceDetailLoadingSubject(false);
    expect(service.getDeviceDetailLoadingSubject().next).toHaveBeenCalledWith(false);
  });

  it('should get device list refresh subject', () => {
    expect(service.getDeviceListRefreshSubject()).toBeInstanceOf(Subject);
  });

  it('should get device detail refresh subject', () => {
    expect(service.getDeviceDetailRefreshSubject()).toBeInstanceOf(Subject);
  });

  it('should get device list filter request parameter subject', () => {
    expect(service.getDeviceListFilterRequestParameterSubject()).toBeInstanceOf(Subject);
  });

  it('should call device list filter request parameter subject', () => {
    const deviceSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    const deviceFilterAction = new DeviceFilterAction(listingPageReloadSubjectParameter, deviceSearchRequest);

    spyOn(service.getDeviceListFilterRequestParameterSubject(), 'next');
    service.callDeviceListFilterRequestParameterSubject(deviceFilterAction);
    expect(service.getDeviceListFilterRequestParameterSubject().next).toHaveBeenCalledWith(deviceFilterAction);
  });

  it('should get transfer device UI subject', () => {
    expect(service.getTransferDeviceUISubject()).toBeInstanceOf(Subject);
  });

  it('should call transfer device UI subject', () => {
    spyOn(service.getTransferDeviceUISubject(), 'next');
    service.callTransferDeviceUISubject(true);
    expect(service.getTransferDeviceUISubject().next).toHaveBeenCalledWith(true);
  });

  // ==================== CACHE MANAGEMENT TESTS ====================

  it('should set and get package version list', () => {
    const packageVersions = ['v1.0.0', 'v1.1.0', 'v2.0.0'];
    service.setPackageVersionList(packageVersions);
    expect(service.getPackageVersionList()).toEqual(packageVersions);
  });

  it('should set and get sales order number list', () => {
    const salesOrderNumbers = ['SO001', 'SO002', 'SO003'];
    service.setSalesOrderNumberList(salesOrderNumbers);
    expect(service.getSalesOrderNumberList()).toEqual(salesOrderNumbers);
  });

  it('should set and get country list', () => {
    const countries: CountryListResponse[] = [
      { id: 1, country: 'USA', languages: ['English'] },
      { id: 2, country: 'Canada', languages: ['English', 'French'] }
    ];
    service.setCountryList(countries);
    expect(service.getCountryList()).toEqual(countries);
  });

  it('should update cache in background with fresh API data', async () => {
    const apiPackageVersions = ['v3.0.0', 'v4.0.0'];
    const apiSalesOrders = ['SO004', 'SO005'];
    const apiCountries: CountryListResponse[] = [
      { id: 3, country: 'Germany', languages: ['German'] }
    ];

    deviceService.getpackageVersion.and.returnValue(of({ body: apiPackageVersions } as any));
    salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(apiSalesOrders));
    countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve(apiCountries));
    commonsService.checkForNull.and.returnValue(apiPackageVersions);

    await service.updateCacheInBackground();

    expect(service.getPackageVersionList()).toEqual(apiPackageVersions);
    expect(service.getSalesOrderNumberList()).toEqual(apiSalesOrders);
    expect(service.getCountryList()).toEqual(apiCountries);
    expect(deviceService.getpackageVersion).toHaveBeenCalled();
    expect(salesOrderApiCallService.getSalesOrderNumberList).toHaveBeenCalled();
    expect(countryCacheService.getCountryListFromCache).toHaveBeenCalledWith(true);
  });

  it('should handle null response from package version API', async () => {
    const mockResponse = { body: null };
    deviceService.getpackageVersion.and.returnValue(of(mockResponse as any));
    salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
    countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));
    commonsService.checkForNull.and.returnValue([]);

    await service.updateCacheInBackground();

    expect(commonsService.checkForNull).toHaveBeenCalledWith(null);
    expect(service.getPackageVersionList()).toEqual([]);
  });

  // ==================== LOADING AND REFRESH PAGE SUBJECT TESTS ====================

  it('should call loading for DeviceListResource', () => {
    spyOn(service, 'callDeviceListLoadingSubject');
    service.isLoading(true, DeviceListResource);
    expect(service.callDeviceListLoadingSubject).toHaveBeenCalledWith(true);
  });

  it('should call loading for DeviceDetailResource', () => {
    spyOn(service, 'callDeviceDetailLoadingSubject');
    service.isLoading(false, DeviceDetailResource);
    expect(service.callDeviceDetailLoadingSubject).toHaveBeenCalledWith(false);
  });

  it('should not call loading for unknown resource', () => {
    spyOn(service, 'callDeviceListLoadingSubject');
    spyOn(service, 'callDeviceDetailLoadingSubject');
    service.isLoading(true, 'UNKNOWN_RESOURCE');
    expect(service.callDeviceListLoadingSubject).not.toHaveBeenCalled();
    expect(service.callDeviceDetailLoadingSubject).not.toHaveBeenCalled();
  });

  it('should call refresh page subject for DeviceListResource with filter hidden', () => {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    const deviceSearchRequest = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);

    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    service.callRefreshPageSubject(
      listingPageReloadSubjectParameter,
      DeviceListResource,
      true,
      deviceSearchRequest
    );

    expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(
      jasmine.any(DeviceFilterAction)
    );
  });

  it('should call refresh page subject for DeviceListResource with filter not hidden', () => {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    const deviceSearchRequest = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);

    spyOn(service.getDeviceListRefreshSubject(), 'next');

    service.callRefreshPageSubject(
      listingPageReloadSubjectParameter,
      DeviceListResource,
      false,
      deviceSearchRequest
    );

    expect(service.getDeviceListRefreshSubject().next).toHaveBeenCalledWith(listingPageReloadSubjectParameter);
  });

  it('should call refresh page subject with clear filter', () => {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, true, false);
    const deviceSearchRequest = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);

    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    service.callRefreshPageSubject(
      listingPageReloadSubjectParameter,
      DeviceListResource,
      true,
      deviceSearchRequest
    );

    const expectedDeviceFilterAction = jasmine.objectContaining({
      deviceSearchRequest: jasmine.objectContaining({
        packageVersions: null
      })
    });

    expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(expectedDeviceFilterAction);
  });

  it('should call refresh page subject for DeviceDetailResource', () => {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    const deviceSearchRequest = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);

    spyOn(service.getDeviceDetailRefreshSubject(), 'next');

    service.callRefreshPageSubject(
      listingPageReloadSubjectParameter,
      DeviceDetailResource,
      true,
      deviceSearchRequest
    );

    expect(service.getDeviceDetailRefreshSubject().next).toHaveBeenCalledWith(listingPageReloadSubjectParameter);
  });

  // ==================== DEVICE LIST OPERATIONS TESTS ====================

  it('should load device list successfully with permission', async () => {
    const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
    const mockPageObj = { page: 0, size: 10 };
    const mockResponse = {
      status: 200,
      body: {
        content: [{ id: 1, deviceId: 'DEV001', editable: true, country: 'USA', deviceType: 'TEST', locked: false, productStatus: 'ENABLED' }],
        numberOfElements: 1,
        totalElements: 1
      }
    };

    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.getDeviceList.and.returnValue(of(mockResponse as any));

    const result = await service.loadDeviceList(mockSearchRequest, mockPageObj);

    expect(result.success).toBe(true);
    expect(result.devices).toEqual(mockResponse.body.content);
    expect(result.totalDeviceDisplay).toBe(1);
    expect(result.totalDevice).toBe(1);
    expect(result.totalItems).toBe(1);
    expect(result.localDeviceList).toEqual([{
      id: 1, deviceId: 'DEV001', editable: true, country: 'USA', deviceType: 'TEST', locked: false, productStatus: 'ENABLED'
    }]);
  });

  it('should return empty result when no permission', async () => {
    const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
    const mockPageObj = { page: 0, size: 10 };

    permissionService.getDevicePermission.and.returnValue(false);

    const result = await service.loadDeviceList(mockSearchRequest, mockPageObj);

    expect(result.success).toBe(false);
    expect(result.devices).toEqual([]);
    expect(result.totalDeviceDisplay).toBe(0);
    expect(result.totalDevice).toBe(0);
    expect(result.localDeviceList).toEqual([]);
    expect(result.totalItems).toBe(0);
  });

  it('should handle non-200 response status', async () => {
    const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
    const mockPageObj = { page: 0, size: 10 };
    const mockResponse = { status: 404, body: null };

    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.getDeviceList.and.returnValue(of(mockResponse as any));

    const result = await service.loadDeviceList(mockSearchRequest, mockPageObj);

    expect(result.success).toBe(false);
    expect(result.devices).toEqual([]);
    expect(result.totalDeviceDisplay).toBe(0);
    expect(result.totalDevice).toBe(0);
    expect(result.localDeviceList).toEqual([]);
    expect(result.totalItems).toBe(0);
  });

  it('should handle API error in loadDeviceList', async () => {
    const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
    const mockPageObj = { page: 0, size: 10 };

    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.getDeviceList.and.returnValue(throwError(() => new Error('API Error')));

    const result = await service.loadDeviceList(mockSearchRequest, mockPageObj);

    expect(result.success).toBe(false);
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
  });

  // ==================== EXPORT CSV TESTS ====================

  it('should export device CSV successfully', async () => {
    const mockDeviceIds = [1, 2, 3];
    const mockGenerateResponse = { body: { fileName: 'devices.csv' } };
    const mockDownloadResponse = { body: 'csv,data' };

    deviceService.generateCSVFileForDevice.and.returnValue(of(mockGenerateResponse as any));
    deviceService.downloadCSVFileForDevice.and.returnValue(of(mockDownloadResponse as any));

    await service.exportDeviceCSV(mockDeviceIds);

    expect(deviceService.generateCSVFileForDevice).toHaveBeenCalledWith(jasmine.any(DeviceExportCSVSearchRequest));
    expect(deviceService.downloadCSVFileForDevice).toHaveBeenCalledWith('devices.csv');
    expect(downloadService.downloadExportCSV).toHaveBeenCalledWith("List_of_Device(s).xls", mockDownloadResponse);
  });

  it('should handle export error', async () => {
    const mockDeviceIds = [1, 2, 3];

    deviceService.generateCSVFileForDevice.and.returnValue(throwError(() => new Error('Export Error')));

    await service.exportDeviceCSV(mockDeviceIds);

    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
  });

  // ==================== DEVICE TYPE CONVERSION TESTS ====================

  it('should convert devices to test type successfully', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [
      { deviceType: 'CLIENT', editable: true, country: 'USA' },
      { deviceType: 'DEMO', editable: true, country: 'USA' }
    ];
    const mockResponse = { status: 200, body: { message: 'Success' } };

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.updateDeviceTypeToTest.and.returnValue(of(mockResponse as any));
    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    const result = await service.convertDevicesToTest(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateDevicePermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource);
    expect(deviceService.updateDeviceTypeToTest).toHaveBeenCalledWith(mockDeviceIds);
    expect(toastrServiceMock.success).toHaveBeenCalledWith(DEVICE_CONVERT_TO_TEST);
    expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalled();
  });

  it('should show info message when devices are already test type', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [
      { deviceType: 'TEST', editable: true, country: 'USA' },
      { deviceType: 'TEST', editable: true, country: 'USA' }
    ];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);

    const result = await service.convertDevicesToTest(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(DEVICE_ALREADY_TEST);
    expect(deviceService.updateDeviceTypeToTest).not.toHaveBeenCalled();
  });

  it('should return false when validation fails for convert to test', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ deviceType: 'CLIENT', editable: true, country: 'USA' }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(false);

    const result = await service.convertDevicesToTest(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(deviceService.updateDeviceTypeToTest).not.toHaveBeenCalled();
  });

  it('should return false when no permission for convert to test', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ deviceType: 'CLIENT', editable: true, country: 'USA' }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(false);

    const result = await service.convertDevicesToTest(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(deviceService.updateDeviceTypeToTest).not.toHaveBeenCalled();
  });

  it('should handle conversion error for convert to test', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ deviceType: 'CLIENT', editable: true, country: 'USA' }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.updateDeviceTypeToTest.and.returnValue(throwError(() => new Error('Conversion Error')));

    const result = await service.convertDevicesToTest(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
  });

  // ==================== CONVERT TO CLIENT TESTS ====================

  it('should convert devices to client type successfully', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ deviceType: 'TEST', editable: true, country: 'USA' }];
    const mockResponse = { status: 200, body: { message: 'Success' } };

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.updateDeviceTypeToClient.and.returnValue(of(mockResponse as any));
    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    const result = await service.convertDevicesToClient(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateDevicePermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource);
    expect(deviceService.updateDeviceTypeToClient).toHaveBeenCalledWith(mockDeviceIds);
    expect(toastrServiceMock.success).toHaveBeenCalledWith(DEVICE_CONVERT_TO_CLIENT);
  });

  it('should show info message when devices are already client type', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [
      { deviceType: 'CLIENT', editable: true, country: 'USA' },
      { deviceType: 'CLIENT', editable: true, country: 'USA' }
    ];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);

    const result = await service.convertDevicesToClient(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(DEVICE_ALREADY_CLIENT);
    expect(deviceService.updateDeviceTypeToClient).not.toHaveBeenCalled();
  });

  it('should handle conversion error for convert to client', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ deviceType: 'TEST', editable: true, country: 'USA' }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.updateDeviceTypeToClient.and.returnValue(throwError(() => new Error('Conversion Error')));

    const result = await service.convertDevicesToClient(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
  });

  // ==================== CONVERT TO DEMO TESTS ====================

  it('should convert devices to demo type successfully', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ deviceType: 'TEST', editable: true, country: 'USA' }];
    const mockResponse = { status: 200, body: { message: 'Success' } };

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.updateDeviceTypeToDemo.and.returnValue(of(mockResponse as any));
    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    const result = await service.convertDevicesToDemo(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateDevicePermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource);
    expect(deviceService.updateDeviceTypeToDemo).toHaveBeenCalledWith(mockDeviceIds);
    expect(toastrServiceMock.success).toHaveBeenCalledWith(DEVICE_CONVERT_TO_DEMO);
  });

  it('should show info message when devices are already demo type', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [
      { deviceType: 'DEMO', editable: true, country: 'USA' },
      { deviceType: 'DEMO', editable: true, country: 'USA' }
    ];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);

    const result = await service.convertDevicesToDemo(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(DEVICE_ALREADY_DEMO);
    expect(deviceService.updateDeviceTypeToDemo).not.toHaveBeenCalled();
  });

  it('should handle conversion error for convert to demo', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ deviceType: 'TEST', editable: true, country: 'USA' }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.updateDeviceTypeToDemo.and.returnValue(throwError(() => new Error('Conversion Error')));

    const result = await service.convertDevicesToDemo(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
  });

  // ==================== TRANSFER DEVICE TESTS ====================

  it('should transfer devices successfully for detail page', async () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{
      deviceSerialNo: 'SN001',
      productStatus: 'ENABLED',
      soStatus: 'CONFIGURED',
      editable: true,
      country: 'USA'
    }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    spyOn(service, 'callTransferDeviceUISubject');

    const result = await service.transferDevices(mockDeviceIds, mockSelectedDevices, DeviceDetailResource);

    expect(result).toBe(true);
    expect(service.validateDevicePermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceDetailResource);
    expect(service.callTransferDeviceUISubject).toHaveBeenCalledWith(true);
  });

  it('should return false when device has no serial number', async () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{
      deviceSerialNo: null,
      productStatus: 'ENABLED',
      soStatus: 'CONFIGURED',
      editable: true,
      country: 'USA'
    }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);

    const result = await service.transferDevices(mockDeviceIds, mockSelectedDevices, DeviceDetailResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(DEVICE_HAS_NO_SERIAL_NUMBER);
  });

  it('should return false when device status is not enabled', async () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{
      deviceSerialNo: 'SN001',
      productStatus: 'DISABLED',
      soStatus: 'CONFIGURED',
      editable: true,
      country: 'USA'
    }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);

    const result = await service.transferDevices(mockDeviceIds, mockSelectedDevices, DeviceDetailResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(DEVICE_STATUS_NOT_ENABLE);
  });

  it('should return false when sales order is partially configured', async () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{
      deviceSerialNo: 'SN001',
      productStatus: 'ENABLED',
      soStatus: 'PARTIALLY_CONFIGURED',
      editable: true,
      country: 'USA'
    }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);

    const result = await service.transferDevices(mockDeviceIds, mockSelectedDevices, DeviceDetailResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(SALES_ORDER_PARTIALLY_CONFIGURED);
  });

  it('should return false for non-detail resource', async () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{
      deviceSerialNo: 'SN001',
      productStatus: 'ENABLED',
      soStatus: 'CONFIGURED',
      editable: true,
      country: 'USA'
    }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);

    const result = await service.transferDevices(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
  });

  it('should handle transfer error', async () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{
      deviceSerialNo: 'SN001',
      productStatus: 'ENABLED',
      soStatus: 'CONFIGURED',
      editable: true,
      country: 'USA'
    }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    spyOn(service, 'callTransferDeviceUISubject').and.throwError('Transfer Error');

    const result = await service.transferDevices(mockDeviceIds, mockSelectedDevices, DeviceDetailResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.error).toHaveBeenCalled();
  });

  // ==================== LOCK/UNLOCK DEVICE TESTS ====================

  it('should lock devices successfully', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ locked: false, editable: true, country: 'USA' }];
    const mockResponse = { status: 200, body: { message: 'Devices locked successfully' } };

    spyOn(service, 'validateDeviceLockUnlockPermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.updateDeviceState.and.returnValue(of(mockResponse as any));
    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    const result = await service.lockUnlockDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateDeviceLockUnlockPermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);
    expect(deviceService.updateDeviceState).toHaveBeenCalledWith(mockDeviceIds, true);
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Devices locked successfully');
  });

  it('should return false when lock validation fails', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ locked: false, editable: true, country: 'USA' }];

    spyOn(service, 'validateDeviceLockUnlockPermissions').and.returnValue(false);

    const result = await service.lockUnlockDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(false);
    expect(deviceService.updateDeviceState).not.toHaveBeenCalled();
  });

  it('should return false when no lock permission', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ locked: false, editable: true, country: 'USA' }];

    spyOn(service, 'validateDeviceLockUnlockPermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(false);

    const result = await service.lockUnlockDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(false);
    expect(deviceService.updateDeviceState).not.toHaveBeenCalled();
  });

  it('should handle lock error', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ locked: false, editable: true, country: 'USA' }];

    spyOn(service, 'validateDeviceLockUnlockPermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.updateDeviceState.and.returnValue(throwError(() => new Error('Lock Error')));

    const result = await service.lockUnlockDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(false);
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
  });

  // ==================== ENABLE/DISABLE DEVICE TESTS ====================

  it('should enable devices successfully', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: false, country: 'USA' }];
    const mockResponse = { body: { message: 'Devices enabled successfully' } };

    spyOn(service, 'validateDeviceEnableDisablePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.editEnableDisableForDevice.and.returnValue(of(mockResponse as any));
    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    const result = await service.enableDisableDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateDeviceEnableDisablePermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);
    expect(deviceService.editEnableDisableForDevice).toHaveBeenCalledWith(mockDeviceIds, true);
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Devices enabled successfully');
  });

  it('should return false when enable validation fails', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: false, country: 'USA' }];

    spyOn(service, 'validateDeviceEnableDisablePermissions').and.returnValue(false);

    const result = await service.enableDisableDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(false);
    expect(deviceService.editEnableDisableForDevice).not.toHaveBeenCalled();
  });

  // ==================== DISABLE PRODUCT STATUS TESTS ====================

  it('should disable product status successfully', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];
    const mockResponse = { body: { message: 'Product status disabled successfully' } };

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    confirmDialogService.getBasicModelConfigForDisableAction.and.returnValue({
      title: 'Confirm', message: 'Are you sure?', btnOkText: 'Yes', btnCancelText: 'No'
    });
    confirmDialogService.confirm.and.returnValue(Promise.resolve(true));
    deviceService.disableProductStatusForDevice.and.returnValue(of(mockResponse as any));
    spyOn(service, 'isLoading');
    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    const result = await service.disableProductStatusForDevices(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateDevicePermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource);
    expect(confirmDialogService.confirm).toHaveBeenCalled();
    expect(deviceService.disableProductStatusForDevice).toHaveBeenCalledWith(mockDeviceIds);
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Product status disabled successfully');
  });

  it('should return false when user cancels disable confirmation', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    confirmDialogService.getBasicModelConfigForDisableAction.and.returnValue({
      title: 'Confirm', message: 'Are you sure?', btnOkText: 'Yes', btnCancelText: 'No'
    });
    confirmDialogService.confirm.and.returnValue(Promise.resolve(false));

    const result = await service.disableProductStatusForDevices(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(deviceService.disableProductStatusForDevice).not.toHaveBeenCalled();
  });

  // ==================== RMA PRODUCT STATUS TESTS ====================

  it('should set RMA product status successfully', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];
    const mockResponse = { body: { message: 'RMA status set successfully' } };

    spyOn(service, 'validateDeviceRMAPermissions').and.returnValue(true);
    confirmDialogService.getBasicModelConfigForDisableAction.and.returnValue({
      title: 'Confirm', message: 'Are you sure?', btnOkText: 'Yes', btnCancelText: 'No'
    });
    confirmDialogService.confirm.and.returnValue(Promise.resolve(true));
    deviceService.rmaProductStatusForDevice.and.returnValue(of(mockResponse as any));
    spyOn(service, 'isLoading');
    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    const result = await service.rmaProductStatusForDevices(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateDeviceRMAPermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource, undefined);
    expect(confirmDialogService.confirm).toHaveBeenCalled();
    expect(deviceService.rmaProductStatusForDevice).toHaveBeenCalledWith(mockDeviceIds);
    expect(toastrServiceMock.success).toHaveBeenCalledWith('RMA status set successfully');
  });

  // ==================== DEVICE VALIDATION TESTS ====================

  it('should validate device selection successfully', () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [
      { editable: true, country: 'USA' },
      { editable: true, country: 'USA' }
    ];

    spyOn(service, 'validateUserPermissionsAndCountry').and.returnValue(true);

    const result = service.validateDeviceSelection(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateUserPermissionsAndCountry).toHaveBeenCalledWith(mockSelectedDevices, DeviceListResource);
  });

  it('should validate user permissions and country', () => {
    const mockSelectedDevices = [
      { editable: true, country: 'USA' },
      { editable: true, country: 'Canada' }
    ];

    moduleValidationService.validateWithEditableWithMultipalRecoard.and.returnValue(true);
    spyOn(service, 'validateUserCountryAccess').and.returnValue(true);

    const result = service.validateUserPermissionsAndCountry(mockSelectedDevices, DeviceListResource);

    expect(result).toBe(true);
    expect(moduleValidationService.validateWithEditableWithMultipalRecoard).toHaveBeenCalled();
    expect(service.validateUserCountryAccess).toHaveBeenCalledWith(mockSelectedDevices, DeviceListResource);
  });

  it('should validate user country access', () => {
    const mockSelectedDevices = [
      { editable: true, country: 'USA' },
      { editable: true, country: 'Canada' }
    ];

    moduleValidationService.validateWithUserCountryForMultileRecord.and.returnValue(true);

    const result = service.validateUserCountryAccess(mockSelectedDevices, DeviceListResource);

    expect(result).toBe(true);
    expect(moduleValidationService.validateWithUserCountryForMultileRecord).toHaveBeenCalledWith(['USA', 'Canada'], DeviceListResource, true);
  });

  it('should get device associated countries', () => {
    const mockSelectedDevices = [
      { editable: true, country: 'USA' },
      { editable: true, country: 'Canada' }
    ];

    const result = service.getDeviceAssociatedCountries(mockSelectedDevices);

    expect(result).toEqual(['USA', 'Canada']);
  });

  it('should validate single device permissions', () => {
    const mockDevice = { editable: true, country: 'USA' };

    moduleValidationService.validateWithEditStateForSingleRecord.and.returnValue(true);
    moduleValidationService.validateWithUserCountryForSingleRecord.and.returnValue(true);

    const result = service.validateSingleDevicePermissions(mockDevice, DeviceDetailResource);

    expect(result).toBe(true);
    expect(moduleValidationService.validateWithEditStateForSingleRecord).toHaveBeenCalledWith(true, DeviceDetailResource);
    expect(moduleValidationService.validateWithUserCountryForSingleRecord).toHaveBeenCalledWith('USA', DeviceDetailResource, true);
  });

  it('should validate single device country access', () => {
    const mockDevice = { editable: true, country: 'USA' };

    moduleValidationService.validateWithUserCountryForSingleRecord.and.returnValue(true);

    const result = service.validateSingleDeviceCountryAccess(mockDevice, DeviceDetailResource);

    expect(result).toBe(true);
    expect(moduleValidationService.validateWithUserCountryForSingleRecord).toHaveBeenCalledWith('USA', DeviceDetailResource, true);
  });

  // ==================== DEVICE DETAIL OPERATIONS TESTS ====================

  it('should load device detail successfully', async () => {
    const mockDeviceId = 123;
    const mockResponse = {
      status: 200,
      body: {
        id: 123,
        deviceId: 'DEV001',
        salesOrderId: 456,
        deviceSerialNo: 'SN001',
        releaseId: 789
      }
    };

    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.getDeviceDetail.and.returnValue(of(mockResponse as any));

    const result = await service.loadDeviceDetail(mockDeviceId);

    expect(result.success).toBe(true);
    expect(result.deviceDetail).toEqual(jasmine.objectContaining({
      id: 123,
      deviceId: 'DEV001',
      salesOrderId: 456,
      deviceSerialNo: 'SN001',
      releaseId: 789
    }));
    expect(result.releaseVersionId).toBe(789);
    expect(result.transferProductDetails).toBeInstanceOf(TransferProductDetails);
  });

  it('should return empty result when no permission for device detail', async () => {
    const mockDeviceId = 123;

    permissionService.getDevicePermission.and.returnValue(false);

    const result = await service.loadDeviceDetail(mockDeviceId);

    expect(result.success).toBe(false);
    expect(result.deviceDetail).toBeNull();
    expect(result.releaseVersionId).toBe(-1);
    expect(result.transferProductDetails).toBeNull();
  });

  it('should handle device detail API error', async () => {
    const mockDeviceId = 123;

    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.getDeviceDetail.and.returnValue(throwError(() => new Error('API Error')));

    const result = await service.loadDeviceDetail(mockDeviceId);

    expect(result.success).toBe(false);
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
  });

  it('should handle device detail with null releaseId', async () => {
    const mockDeviceId = 123;
    const mockResponse = {
      status: 200,
      body: {
        id: 123,
        deviceId: 'DEV001',
        salesOrderId: 456,
        deviceSerialNo: 'SN001',
        releaseId: null
      }
    };

    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.getDeviceDetail.and.returnValue(of(mockResponse as any));

    const result = await service.loadDeviceDetail(mockDeviceId);

    expect(result.success).toBe(true);
    expect(result.releaseVersionId).toBe(-1);
  });

  it('should handle non-200 response for device detail', async () => {
    const mockDeviceId = 123;
    const mockResponse = { status: 404, body: null };

    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.getDeviceDetail.and.returnValue(of(mockResponse as any));

    const result = await service.loadDeviceDetail(mockDeviceId);

    expect(result.success).toBe(false);
    expect(result.deviceDetail).toBeNull();
    expect(result.releaseVersionId).toBe(-1);
    expect(result.transferProductDetails).toBeNull();
  });

  // ==================== RELEASE VERSION TESTS ====================

  it('should get release versions for test device with permission', async () => {
    const mockResponse = { body: [{ id: 1, version: 'v1.0.0' }, { id: 2, version: 'v2.0.0' }] };

    deviceService.getReleaseVersionDetail.and.returnValue(of(mockResponse as any));

    const result = await service.getReleaseVersions(deviceTypesEnum.TEST_DEVICE, 1, 'v1.0.0', true);

    expect(result.success).toBe(true);
    expect(result.releaseVersions).toEqual(mockResponse.body);
    expect(result.selectedReleaseVersion).toBe(-1);
    expect(result.btnReleaseVersionDisable).toBe(true);
  });

  it('should return empty result for non-test device', async () => {
    const result = await service.getReleaseVersions(deviceTypesEnum.CLIENT_DEVICE, 1, 'v1.0.0', true);

    expect(result.success).toBe(true);
    expect(result.releaseVersions).toEqual([]);
    expect(result.selectedReleaseVersion).toBe(-1);
    expect(result.btnReleaseVersionDisable).toBe(true);
  });

  it('should return empty result when no update permission', async () => {
    const result = await service.getReleaseVersions(deviceTypesEnum.TEST_DEVICE, 1, 'v1.0.0', false);

    expect(result.success).toBe(true);
    expect(result.releaseVersions).toEqual([]);
    expect(result.selectedReleaseVersion).toBe(-1);
    expect(result.btnReleaseVersionDisable).toBe(true);
  });

  it('should return empty result when countryId is null', async () => {
    const result = await service.getReleaseVersions(deviceTypesEnum.TEST_DEVICE, null, 'v1.0.0', true);

    expect(result.success).toBe(true);
    expect(result.releaseVersions).toEqual([]);
    expect(result.selectedReleaseVersion).toBe(-1);
    expect(result.btnReleaseVersionDisable).toBe(true);
  });

  it('should handle release version API error', async () => {
    deviceService.getReleaseVersionDetail.and.returnValue(throwError(() => new Error('API Error')));

    const result = await service.getReleaseVersions(deviceTypesEnum.TEST_DEVICE, 1, 'v1.0.0', true);

    expect(result.success).toBe(true);
    expect(result.releaseVersions).toEqual([]);
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
  });

  it('should assign release version successfully', async () => {
    const mockResponse = { status: 200, body: { message: 'Success' } };

    deviceService.assignSelectedReleaseVersion.and.returnValue(of(mockResponse as any));

    await service.assignReleaseVersion(123, 456);

    expect(deviceService.assignSelectedReleaseVersion).toHaveBeenCalledWith(jasmine.any(AssignSelectedReleaseVersionRequest));
    expect(toastrServiceMock.success).toHaveBeenCalledWith("Release version assigned successfully");
  });

  it('should handle non-200 response for assign release version', async () => {
    const mockResponse = { status: 400, body: { message: 'Error' } };

    deviceService.assignSelectedReleaseVersion.and.returnValue(of(mockResponse as any));

    await service.assignReleaseVersion(123, 456);

    expect(toastrServiceMock.error).toHaveBeenCalledWith("Error in assigning Release version");
  });

  it('should handle assign release version API error', async () => {
    deviceService.assignSelectedReleaseVersion.and.returnValue(throwError(() => new Error('API Error')));

    await service.assignReleaseVersion(123, 456);

    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
  });

  it('should disable assign button when no selection', () => {
    const result = service.shouldDisableAssignButton(-1, 123);
    expect(result).toBe(true);
  });

  it('should disable assign button when same version selected', () => {
    const result = service.shouldDisableAssignButton(123, 123);
    expect(result).toBe(true);
  });

  it('should enable assign button when different version selected', () => {
    const result = service.shouldDisableAssignButton(456, 123);
    expect(result).toBe(false);
  });

  // ==================== ASSOCIATE DEVICES WITH SALES ORDER TESTS ====================

  it('should associate devices with sales order successfully', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];
    const mockSalesOrderData = { id: 123, orderNumber: 'SO001' };
    const mockResponse = { body: { message: 'Association successful' } };

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    deviceService.associationDeviceWithSalesOrder.and.returnValue(of(mockResponse as any));
    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    const result = await service.associateDevicesWithSalesOrder(
      mockDeviceIds,
      mockSelectedDevices,
      mockSalesOrderData,
      false,
      DeviceListResource
    );

    expect(result).toBe(true);
    expect(service.validateDevicePermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource);
    expect(deviceService.associationDeviceWithSalesOrder).toHaveBeenCalledWith(mockDeviceIds, jasmine.any(Object));
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Association successful');
  });

  it('should associate devices and update cache for new sales order', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];
    const mockSalesOrderData = { id: 123, orderNumber: 'SO001' };
    const mockResponse = { body: { message: 'Association successful' } };
    const mockSalesOrderNumbers = ['SO001', 'SO002'];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    deviceService.associationDeviceWithSalesOrder.and.returnValue(of(mockResponse as any));
    salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(mockSalesOrderNumbers));
    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    const result = await service.associateDevicesWithSalesOrder(
      mockDeviceIds,
      mockSelectedDevices,
      mockSalesOrderData,
      true,
      DeviceListResource
    );

    expect(result).toBe(true);
    expect(salesOrderApiCallService.getSalesOrderNumberList).toHaveBeenCalled();
    expect(service.getSalesOrderNumberList()).toEqual(mockSalesOrderNumbers);
  });

  it('should handle association error', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];
    const mockSalesOrderData = { id: 123, orderNumber: 'SO001' };

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    deviceService.associationDeviceWithSalesOrder.and.returnValue(throwError(() => new Error('Association Error')));

    const result = await service.associateDevicesWithSalesOrder(
      mockDeviceIds,
      mockSelectedDevices,
      mockSalesOrderData,
      false,
      DeviceListResource
    );

    expect(result).toBe(false);
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
  });

  // ==================== VALIDATION PERMISSION TESTS ====================

  it('should validate device permissions for DeviceListResource', () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    spyOn(service, 'validateDeviceSelection').and.returnValue(true);

    const result = service.validateDevicePermissions(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateDeviceSelection).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource);
  });

  it('should validate device permissions for DeviceDetailResource', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    spyOn(service, 'validateSingleDevicePermissions').and.returnValue(true);

    const result = service.validateDevicePermissions(mockDeviceIds, mockSelectedDevices, DeviceDetailResource);

    expect(result).toBe(true);
    expect(service.validateSingleDevicePermissions).toHaveBeenCalledWith(mockSelectedDevices[0], DeviceDetailResource);
  });

  it('should return false for unknown resource in validateDevicePermissions', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    const result = service.validateDevicePermissions(mockDeviceIds, mockSelectedDevices, 'UNKNOWN_RESOURCE');

    expect(result).toBe(false);
  });

  it('should validate device lock/unlock permissions for DeviceListResource', () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ locked: false, editable: true, country: 'USA' }];

    spyOn(service, 'validateDeviceSelection').and.returnValue(true);

    const result = service.validateDeviceLockUnlockPermissions(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateDeviceSelection).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource);
  });

  it('should validate device lock/unlock permissions for DeviceDetailResource', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ locked: false, editable: true, country: 'USA' }];

    spyOn(service, 'validateSingleDevicePermissions').and.returnValue(true);

    const result = service.validateDeviceLockUnlockPermissions(mockDeviceIds, mockSelectedDevices, true, DeviceDetailResource);

    expect(result).toBe(true);
    expect(service.validateSingleDevicePermissions).toHaveBeenCalledWith(mockSelectedDevices[0], DeviceDetailResource);
  });

  it('should return false when device is already locked', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ locked: true, editable: true, country: 'USA' }];

    spyOn(service, 'validateSingleDevicePermissions').and.returnValue(true);

    const result = service.validateDeviceLockUnlockPermissions(mockDeviceIds, mockSelectedDevices, true, DeviceDetailResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(DEVICE_ALREADY_LOCKED);
  });

  it('should return false when device is already unlocked', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ locked: false, editable: true, country: 'USA' }];

    spyOn(service, 'validateSingleDevicePermissions').and.returnValue(true);

    const result = service.validateDeviceLockUnlockPermissions(mockDeviceIds, mockSelectedDevices, false, DeviceDetailResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(DEVICE_ALREADY_UNLOCKED);
  });

  it('should return false for unknown resource in validateDeviceLockUnlockPermissions', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ locked: false, editable: true, country: 'USA' }];

    const result = service.validateDeviceLockUnlockPermissions(mockDeviceIds, mockSelectedDevices, true, 'UNKNOWN_RESOURCE');

    expect(result).toBe(false);
  });

  it('should validate device enable/disable permissions for DeviceListResource', () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: false, country: 'USA' }];

    spyOn(service, 'validateUserCountryAccess').and.returnValue(true);

    const result = service.validateDeviceEnableDisablePermissions(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateUserCountryAccess).toHaveBeenCalledWith(mockSelectedDevices, DeviceListResource);
  });

  it('should validate device enable/disable permissions for DeviceDetailResource', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: false, country: 'USA' }];

    spyOn(service, 'validateSingleDeviceCountryAccess').and.returnValue(true);

    const result = service.validateDeviceEnableDisablePermissions(mockDeviceIds, mockSelectedDevices, true, DeviceDetailResource);

    expect(result).toBe(true);
    expect(service.validateSingleDeviceCountryAccess).toHaveBeenCalledWith(mockSelectedDevices[0], DeviceDetailResource);
  });

  it('should return false when device is already enabled', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    spyOn(service, 'validateSingleDeviceCountryAccess').and.returnValue(true);

    const result = service.validateDeviceEnableDisablePermissions(mockDeviceIds, mockSelectedDevices, true, DeviceDetailResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(DEVICE_ALREADY_EDIT_ENABLE);
  });

  it('should return false when device is already disabled', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: false, country: 'USA' }];

    spyOn(service, 'validateSingleDeviceCountryAccess').and.returnValue(true);

    const result = service.validateDeviceEnableDisablePermissions(mockDeviceIds, mockSelectedDevices, false, DeviceDetailResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(DEVICE_ALREADY_EDIT_DISABLE);
  });

  it('should return false for unknown resource in validateDeviceEnableDisablePermissions', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: false, country: 'USA' }];

    const result = service.validateDeviceEnableDisablePermissions(mockDeviceIds, mockSelectedDevices, true, 'UNKNOWN_RESOURCE');

    expect(result).toBe(false);
  });

  // ==================== RMA VALIDATION TESTS ====================

  it('should validate device RMA permissions for DeviceListResource', () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    spyOn(service, 'validateDeviceSelection').and.returnValue(true);

    const result = service.validateDeviceRMAPermissions(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateDeviceSelection).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource);
  });

  it('should validate device RMA permissions for DeviceDetailResource', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: true, country: 'USA', productStatus: 'ENABLED' }];
    const mockProductStatusList: EnumMapping[] = [{ key: 'ENABLED', value: 'Enabled' }];
    const enumMappingDisplayNamePipeSpy = TestBed.inject(EnumMappingDisplayNamePipe) as jasmine.SpyObj<EnumMappingDisplayNamePipe>;
    const validationServiceSpy = TestBed.inject(ValidationService) as jasmine.SpyObj<ValidationService>;

    spyOn(service, 'validateSingleDevicePermissions').and.returnValue(true);
    enumMappingDisplayNamePipeSpy.transform.and.returnValue('Enabled');
    validationServiceSpy.validateProductStatusForRMAAction.and.returnValue(true);

    const result = service.validateDeviceRMAPermissions(mockDeviceIds, mockSelectedDevices, DeviceDetailResource, mockProductStatusList);

    expect(result).toBe(true);
    expect(service.validateSingleDevicePermissions).toHaveBeenCalledWith(mockSelectedDevices[0], DeviceDetailResource);
    expect(enumMappingDisplayNamePipeSpy.transform).toHaveBeenCalledWith('ENABLED', mockProductStatusList);
    expect(validationServiceSpy.validateProductStatusForRMAAction).toHaveBeenCalledWith('Enabled');
  });

  it('should return false when RMA validation fails for product status', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: true, country: 'USA', productStatus: 'DISABLED' }];
    const mockProductStatusList: EnumMapping[] = [{ key: 'DISABLED', value: 'Disabled' }];
    const enumMappingDisplayNamePipeSpy = TestBed.inject(EnumMappingDisplayNamePipe) as jasmine.SpyObj<EnumMappingDisplayNamePipe>;
    const validationServiceSpy = TestBed.inject(ValidationService) as jasmine.SpyObj<ValidationService>;

    spyOn(service, 'validateSingleDevicePermissions').and.returnValue(true);
    enumMappingDisplayNamePipeSpy.transform.and.returnValue('Disabled');
    validationServiceSpy.validateProductStatusForRMAAction.and.returnValue(false);

    const result = service.validateDeviceRMAPermissions(mockDeviceIds, mockSelectedDevices, DeviceDetailResource, mockProductStatusList);

    expect(result).toBe(false);
    expect(confirmDialogService.getErrorMessageDisableToRma).toHaveBeenCalledWith(DeviceDetailResource);
  });

  it('should return false for unknown resource in validateDeviceRMAPermissions', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    const result = service.validateDeviceRMAPermissions(mockDeviceIds, mockSelectedDevices, 'UNKNOWN_RESOURCE');

    expect(result).toBe(false);
  });

  // ==================== FILTER VALIDATION TESTS ====================

  it('should validate filter form with valid data', () => {
    const mockFormValue = {
      deviceId: 'DEV001',
      deviceSerialNo: null,
      customerName: null,
      packageVersions: null,
      connectionState: null,
      deviceLockState: null,
      deviceEditState: null,
      countries: null,
      drpDeviceType: null,
      salesOrderNumber: null,
      productStatus: null
    };

    commonsService.checkValueIsNullOrEmpty.and.returnValues(false, true, true, true, true, true, true, true, true, true, true);

    const result = service.validateFilterForm(mockFormValue);

    expect(result).toBe(true);
  });

  it('should return false when all filter fields are empty', () => {
    const mockFormValue = {
      deviceId: null,
      deviceSerialNo: null,
      customerName: null,
      packageVersions: null,
      connectionState: null,
      deviceLockState: null,
      deviceEditState: null,
      countries: null,
      drpDeviceType: null,
      salesOrderNumber: null,
      productStatus: null
    };

    commonsService.checkValueIsNullOrEmpty.and.returnValue(true);

    const result = service.validateFilterForm(mockFormValue);

    expect(result).toBe(false);
  });

  it('should build device search request from form values', () => {
    const mockFormValue = {
      deviceId: 'DEV001',
      customerName: 'Test Customer',
      deviceSerialNo: 'SN001',
      countries: [{ id: 1, name: 'USA' }],
      productStatus: ['ENABLED'],
      connectionState: ['CONNECTED'],
      deviceLockState: [{ key: true, value: 'Locked' }],
      deviceEditState: [{ key: false, value: 'Disabled' }],
      packageVersions: 'v1.0.0',
      drpDeviceType: 'TEST',
      salesOrderNumber: 'SO001'
    };

    commonsService.checkNullFieldValue.and.returnValues('DEV001', 'Test Customer', 'SN001', 'v1.0.0', 'SO001');
    commonsService.getIdsFromArray.and.returnValue([1]);
    commonsService.getSelectedValueFromEnum.and.returnValues(['ENABLED'], ['CONNECTED']);
    commonsService.getSelectedValueFromBooleanKeyValueMapping.and.returnValues(true, false);
    commonsService.getDeviceTypeStringToEnum.and.returnValue('TEST');

    const result = service.buildDeviceSearchRequest(mockFormValue);

    expect(result).toBeInstanceOf(DeviceSearchRequest);
    expect(commonsService.checkNullFieldValue).toHaveBeenCalledTimes(5);
    expect(commonsService.getIdsFromArray).toHaveBeenCalledWith([{ id: 1, name: 'USA' }]);
    expect(commonsService.getSelectedValueFromEnum).toHaveBeenCalledTimes(2);
    expect(commonsService.getSelectedValueFromBooleanKeyValueMapping).toHaveBeenCalledTimes(2);
    expect(commonsService.getDeviceTypeStringToEnum).toHaveBeenCalledWith('TEST');
  });

  it('should handle null countries in buildDeviceSearchRequest', () => {
    const mockFormValue = {
      deviceId: null,
      customerName: null,
      deviceSerialNo: null,
      countries: null,
      productStatus: null,
      connectionState: null,
      deviceLockState: null,
      deviceEditState: null,
      packageVersions: null,
      drpDeviceType: null,
      salesOrderNumber: null
    };

    commonsService.checkNullFieldValue.and.returnValue(null);
    commonsService.getSelectedValueFromEnum.and.returnValue([]);
    commonsService.getSelectedValueFromBooleanKeyValueMapping.and.returnValue(null);
    commonsService.getDeviceTypeStringToEnum.and.returnValue(null);

    const result = service.buildDeviceSearchRequest(mockFormValue);

    expect(result).toBeInstanceOf(DeviceSearchRequest);
  });

  it('should process filter search successfully', () => {
    const mockFormValue = { deviceId: 'DEV001' };
    const mockListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

    spyOn(service, 'validateFilterForm').and.returnValue(true);
    spyOn(service, 'buildDeviceSearchRequest').and.returnValue(new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null));
    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    const result = service.processFilterSearch(mockFormValue, false, mockListingPageReloadSubjectParameter);

    expect(result).toBe(true);
    expect(service.validateFilterForm).toHaveBeenCalledWith(mockFormValue);
    expect(service.buildDeviceSearchRequest).toHaveBeenCalledWith(mockFormValue);
    expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalled();
  });

  it('should return false when form is invalid', () => {
    const mockFormValue = { deviceId: null };
    const mockListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

    const result = service.processFilterSearch(mockFormValue, true, mockListingPageReloadSubjectParameter);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(COMMON_SELECT_FILTER);
  });

  it('should return false when filter validation fails', () => {
    const mockFormValue = { deviceId: null };
    const mockListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

    spyOn(service, 'validateFilterForm').and.returnValue(false);

    const result = service.processFilterSearch(mockFormValue, false, mockListingPageReloadSubjectParameter);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(COMMON_SELECT_FILTER);
  });

  it('should clear all filters and refresh', () => {
    const mockListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, true, false);

    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    service.clearAllFiltersAndRefresh(mockListingPageReloadSubjectParameter);

    expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(jasmine.any(DeviceFilterAction));
  });

  // ==================== ADDITIONAL VALIDATION TESTS ====================

  it('should return false when edit state validation fails', () => {
    const mockSelectedDevices = [{ editable: false, country: 'USA' }];

    moduleValidationService.validateWithEditableWithMultipalRecoard.and.returnValue(false);

    const result = service.validateUserPermissionsAndCountry(mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(moduleValidationService.validateWithEditableWithMultipalRecoard).toHaveBeenCalled();
  });

  it('should return false when single device edit state validation fails', () => {
    const mockDevice = { editable: false, country: 'USA' };

    moduleValidationService.validateWithEditStateForSingleRecord.and.returnValue(false);

    const result = service.validateSingleDevicePermissions(mockDevice, DeviceDetailResource);

    expect(result).toBe(false);
    expect(moduleValidationService.validateWithEditStateForSingleRecord).toHaveBeenCalledWith(false, DeviceDetailResource);
  });

  it('should return false when single device country validation fails', () => {
    const mockDevice = { editable: true, country: 'USA' };

    moduleValidationService.validateWithEditStateForSingleRecord.and.returnValue(true);
    moduleValidationService.validateWithUserCountryForSingleRecord.and.returnValue(false);

    const result = service.validateSingleDevicePermissions(mockDevice, DeviceDetailResource);

    expect(result).toBe(false);
  });

  it('should return false when single device permissions validation fails for RMA', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: false, country: 'USA' }];

    spyOn(service, 'validateSingleDevicePermissions').and.returnValue(false);

    const result = service.validateDeviceRMAPermissions(mockDeviceIds, mockSelectedDevices, DeviceDetailResource);

    expect(result).toBe(false);
    expect(service.validateSingleDevicePermissions).toHaveBeenCalledWith(mockSelectedDevices[0], DeviceDetailResource);
  });

  it('should return false when single device country access validation fails for enable/disable', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: false, country: 'USA' }];

    spyOn(service, 'validateSingleDeviceCountryAccess').and.returnValue(false);

    const result = service.validateDeviceEnableDisablePermissions(mockDeviceIds, mockSelectedDevices, true, DeviceDetailResource);

    expect(result).toBe(false);
    expect(service.validateSingleDeviceCountryAccess).toHaveBeenCalledWith(mockSelectedDevices[0], DeviceDetailResource);
  });

  it('should return false when single device permissions validation fails for lock/unlock', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ locked: false, editable: false, country: 'USA' }];

    spyOn(service, 'validateSingleDevicePermissions').and.returnValue(false);

    const result = service.validateDeviceLockUnlockPermissions(mockDeviceIds, mockSelectedDevices, true, DeviceDetailResource);

    expect(result).toBe(false);
    expect(service.validateSingleDevicePermissions).toHaveBeenCalledWith(mockSelectedDevices[0], DeviceDetailResource);
  });

  it('should handle connection state with single value in buildDeviceSearchRequest', () => {
    const mockFormValue = {
      deviceId: null,
      customerName: null,
      deviceSerialNo: null,
      countries: null,
      productStatus: null,
      connectionState: ['CONNECTED'],
      deviceLockState: null,
      deviceEditState: null,
      packageVersions: null,
      drpDeviceType: null,
      salesOrderNumber: null
    };

    commonsService.checkNullFieldValue.and.returnValue(null);
    commonsService.getSelectedValueFromEnum.and.returnValues([], ['CONNECTED']);
    commonsService.getSelectedValueFromBooleanKeyValueMapping.and.returnValue(null);
    commonsService.getDeviceTypeStringToEnum.and.returnValue(null);

    const result = service.buildDeviceSearchRequest(mockFormValue);

    expect(result).toBeInstanceOf(DeviceSearchRequest);
  });

  it('should handle connection state with multiple values in buildDeviceSearchRequest', () => {
    const mockFormValue = {
      deviceId: null,
      customerName: null,
      deviceSerialNo: null,
      countries: null,
      productStatus: null,
      connectionState: ['CONNECTED', 'DISCONNECTED'],
      deviceLockState: null,
      deviceEditState: null,
      packageVersions: null,
      drpDeviceType: null,
      salesOrderNumber: null
    };

    commonsService.checkNullFieldValue.and.returnValue(null);
    commonsService.getSelectedValueFromEnum.and.returnValues([], ['CONNECTED', 'DISCONNECTED']);
    commonsService.getSelectedValueFromBooleanKeyValueMapping.and.returnValue(null);
    commonsService.getDeviceTypeStringToEnum.and.returnValue(null);

    const result = service.buildDeviceSearchRequest(mockFormValue);

    expect(result).toBeInstanceOf(DeviceSearchRequest);
  });

  it('should return false when no permission for disable product status', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(false);

    const result = await service.disableProductStatusForDevices(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(deviceService.disableProductStatusForDevice).not.toHaveBeenCalled();
  });

  it('should handle disable product status error', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    confirmDialogService.getBasicModelConfigForDisableAction.and.returnValue({
      title: 'Confirm', message: 'Are you sure?', btnOkText: 'Yes', btnCancelText: 'No'
    });
    confirmDialogService.confirm.and.returnValue(Promise.resolve(true));
    deviceService.disableProductStatusForDevice.and.returnValue(throwError(() => new Error('Disable Error')));
    spyOn(service, 'isLoading');

    const result = await service.disableProductStatusForDevices(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
  });

  it('should return false when user cancels RMA confirmation', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    spyOn(service, 'validateDeviceRMAPermissions').and.returnValue(true);
    confirmDialogService.getBasicModelConfigForDisableAction.and.returnValue({
      title: 'Confirm', message: 'Are you sure?', btnOkText: 'Yes', btnCancelText: 'No'
    });
    confirmDialogService.confirm.and.returnValue(Promise.resolve(false));

    const result = await service.rmaProductStatusForDevices(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(deviceService.rmaProductStatusForDevice).not.toHaveBeenCalled();
  });

  it('should handle RMA error', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    spyOn(service, 'validateDeviceRMAPermissions').and.returnValue(true);
    confirmDialogService.getBasicModelConfigForDisableAction.and.returnValue({
      title: 'Confirm', message: 'Are you sure?', btnOkText: 'Yes', btnCancelText: 'No'
    });
    confirmDialogService.confirm.and.returnValue(Promise.resolve(true));
    deviceService.rmaProductStatusForDevice.and.returnValue(throwError(() => new Error('RMA Error')));
    spyOn(service, 'isLoading');

    const result = await service.rmaProductStatusForDevices(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
  });

  it('should return false when enable/disable permission validation fails', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: false, country: 'USA' }];

    spyOn(service, 'validateDeviceEnableDisablePermissions').and.returnValue(false);

    const result = await service.enableDisableDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(false);
    expect(deviceService.editEnableDisableForDevice).not.toHaveBeenCalled();
  });

  it('should return false when no permission for enable/disable', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: false, country: 'USA' }];

    spyOn(service, 'validateDeviceEnableDisablePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(false);

    const result = await service.enableDisableDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(false);
    expect(deviceService.editEnableDisableForDevice).not.toHaveBeenCalled();
  });

  it('should handle enable/disable error', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: false, country: 'USA' }];

    spyOn(service, 'validateDeviceEnableDisablePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.editEnableDisableForDevice.and.returnValue(throwError(() => new Error('Enable/Disable Error')));

    const result = await service.enableDisableDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(false);
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
  });

  it('should return false when lock/unlock returns non-200 status', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ locked: false, editable: true, country: 'USA' }];
    const mockResponse = { status: 400, body: { message: 'Error' } };

    spyOn(service, 'validateDeviceLockUnlockPermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.updateDeviceState.and.returnValue(of(mockResponse as any));

    const result = await service.lockUnlockDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(false);
  });

  it('should return false when validation fails for transfer devices', async () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{
      deviceSerialNo: 'SN001',
      productStatus: 'ENABLED',
      soStatus: 'CONFIGURED',
      editable: false,
      country: 'USA'
    }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(false);

    const result = await service.transferDevices(mockDeviceIds, mockSelectedDevices, DeviceDetailResource);

    expect(result).toBe(false);
  });

  it('should return false when validation fails for associate devices', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: false, country: 'USA' }];
    const mockSalesOrderData = { id: 123, orderNumber: 'SO001' };

    spyOn(service, 'validateDevicePermissions').and.returnValue(false);

    const result = await service.associateDevicesWithSalesOrder(
      mockDeviceIds,
      mockSelectedDevices,
      mockSalesOrderData,
      false,
      DeviceListResource
    );

    expect(result).toBe(false);
    expect(deviceService.associationDeviceWithSalesOrder).not.toHaveBeenCalled();
  });

  // ==================== ADDITIONAL EDGE CASE TESTS ====================

  it('should handle null deviceSearchRequestBodyApply in callRefreshPageSubject', () => {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    service.callRefreshPageSubject(
      listingPageReloadSubjectParameter,
      DeviceListResource,
      true,
      null
    );

    expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(
      jasmine.any(DeviceFilterAction)
    );
  });

  it('should handle undefined deviceSearchRequestBodyApply in callRefreshPageSubject', () => {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    service.callRefreshPageSubject(
      listingPageReloadSubjectParameter,
      DeviceListResource,
      true,
      undefined
    );

    expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(
      jasmine.any(DeviceFilterAction)
    );
  });

  it('should handle empty response body in processDeviceListResponse', () => {
    const emptyResponseBody = { totalElements: '0' };

    const result = (service as any).processDeviceListResponse(emptyResponseBody);

    expect(result.devices).toEqual([]);
    expect(result.totalDeviceDisplay).toBe(0);
    expect(result.totalDevice).toBe('0');
    expect(result.localDeviceList).toEqual([]);
    expect(result.totalItems).toBe(0);
  });

  it('should handle response body with undefined totalElements in processDeviceListResponse', () => {
    const responseBodyWithUndefined = { content: [], numberOfElements: 0 };

    const result = (service as any).processDeviceListResponse(responseBodyWithUndefined);

    expect(result.devices).toEqual([]);
    expect(result.totalDeviceDisplay).toBe(0);
    expect(result.totalDevice).toBe(0);
    expect(result.localDeviceList).toEqual([]);
    expect(result.totalItems).toBeNaN();
  });

  it('should handle empty device content in extractLocalDeviceIds', () => {
    const emptyDeviceContent: any[] = [];

    const result = (service as any).extractLocalDeviceIds(emptyDeviceContent);

    expect(result).toEqual([]);
  });

  it('should call showSuccessAndRefresh for DeviceListResource', () => {
    spyOn(service as any, 'listPageRefresh');
    spyOn(service as any, 'detailPageRefresh');

    (service as any).showSuccessAndRefresh('Test message', DeviceListResource);

    expect(toastrServiceMock.success).toHaveBeenCalledWith('Test message');
    expect((service as any).listPageRefresh).toHaveBeenCalled();
    expect((service as any).detailPageRefresh).not.toHaveBeenCalled();
  });

  it('should call showSuccessAndRefresh for DeviceDetailResource', () => {
    spyOn(service as any, 'listPageRefresh');
    spyOn(service as any, 'detailPageRefresh');

    (service as any).showSuccessAndRefresh('Test message', DeviceDetailResource);

    expect(toastrServiceMock.success).toHaveBeenCalledWith('Test message');
    expect((service as any).detailPageRefresh).toHaveBeenCalled();
    expect((service as any).listPageRefresh).not.toHaveBeenCalled();
  });

  it('should call showSuccessAndRefresh for unknown resource', () => {
    spyOn(service as any, 'listPageRefresh');
    spyOn(service as any, 'detailPageRefresh');

    (service as any).showSuccessAndRefresh('Test message', 'UNKNOWN_RESOURCE');

    expect(toastrServiceMock.success).toHaveBeenCalledWith('Test message');
    expect((service as any).listPageRefresh).not.toHaveBeenCalled();
    expect((service as any).detailPageRefresh).not.toHaveBeenCalled();
  });

  it('should call listPageRefresh', () => {
    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    (service as any).listPageRefresh();

    expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(jasmine.any(DeviceFilterAction));
  });

  it('should call detailPageRefresh', () => {
    spyOn(service.getDeviceDetailRefreshSubject(), 'next');

    (service as any).detailPageRefresh();

    expect(service.getDeviceDetailRefreshSubject().next).toHaveBeenCalledWith(jasmine.any(ListingPageReloadSubjectParameter));
  });

  it('should call getEmptyReleaseVersionsResult', () => {
    const result = (service as any).getEmptyReleaseVersionsResult();

    expect(result.success).toBe(true);
    expect(result.releaseVersions).toEqual([]);
    expect(result.selectedReleaseVersion).toBe(-1);
    expect(result.btnReleaseVersionDisable).toBe(true);
  });

  it('should call deviceEmptyResponse', () => {
    const result = (service as any).deviceEmptyResponse();

    expect(result.success).toBe(false);
    expect(result.deviceDetail).toBeNull();
    expect(result.releaseVersionId).toBe(-1);
    expect(result.transferProductDetails).toBeNull();
  });

  it('should handle error in transfer devices with error object', async () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{
      deviceSerialNo: 'SN001',
      productStatus: 'ENABLED',
      soStatus: 'CONFIGURED',
      editable: true,
      country: 'USA'
    }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    const customError = new Error('Transfer Error') as any;
    customError.error = { message: 'Custom error message' };
    spyOn(service, 'callTransferDeviceUISubject').and.throwError(customError);

    const result = await service.transferDevices(mockDeviceIds, mockSelectedDevices, DeviceDetailResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.error).toHaveBeenCalledWith('Custom error message');
  });

  it('should handle error in transfer devices without error object', async () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{
      deviceSerialNo: 'SN001',
      productStatus: 'ENABLED',
      soStatus: 'CONFIGURED',
      editable: true,
      country: 'USA'
    }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    spyOn(service, 'callTransferDeviceUISubject').and.throwError('Simple error');

    const result = await service.transferDevices(mockDeviceIds, mockSelectedDevices, DeviceDetailResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.error).toHaveBeenCalledWith('Transfer device operation failed');
  });

  it('should validate device type conversion when all devices are same type', () => {
    const mockSelectedDevices = [
      { deviceType: 'TEST' },
      { deviceType: 'TEST' }
    ];

    const result = (service as any).validateDeviceTypeConversion('TEST', mockSelectedDevices);

    expect(result).toBe(false);
  });

  it('should validate device type conversion when devices are different types', () => {
    const mockSelectedDevices = [
      { deviceType: 'TEST' },
      { deviceType: 'CLIENT' }
    ];

    const result = (service as any).validateDeviceTypeConversion('TEST', mockSelectedDevices);

    expect(result).toBe(true);
  });

  it('should handle null response body in getReleaseVersions', async () => {
    const mockResponse = { body: null };

    deviceService.getReleaseVersionDetail.and.returnValue(of(mockResponse as any));

    const result = await service.getReleaseVersions(deviceTypesEnum.TEST_DEVICE, 1, 'v1.0.0', true);

    expect(result.success).toBe(true);
    expect(result.releaseVersions).toEqual([]);
  });

});
